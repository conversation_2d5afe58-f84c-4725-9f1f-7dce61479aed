import { Hono } from "hono";
import { HTTPException } from "hono/http-exception";
import { handle } from "hono/vercel";

export const config = {
  runtime: "edge",
};

const app = new Hono().basePath("/api");

app.get("/play", async (c) => {
  let url = 'https://delta.123tokyo.xyz/get.php/7/a1/knW7-x7Y7RE.mp3?n=S%C6%A0N%20T%C3%99NG%20M-TP%20_%20H%C3%83Y%20TRAO%20CHO%20ANH%20ft.%20Snoop%20Dogg%20_%20Official%20MV&uT=R&uN=YWx0YXI3YmUyMzh6bGdtcjRw&h=v3rRjC6znRSwtPwPncVzGw&s=1753859962&uT=R&uN=YWx0YXI3YmUyMzh6bGdtcjRw';

  if (!url) {
    throw new HTTPException(404, {
      message: "Missing required 'url' parameter",
    });
  }

  return fetch(url);
});

export default handle(app);
